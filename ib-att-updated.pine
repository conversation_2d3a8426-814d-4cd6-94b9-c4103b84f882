//@version=5
indicator("Initial Balance-Advanced Time Technique", shorttitle="IB-ATT", overlay=true, max_bars_back=5000)

//----IB indicator -----
// Options
ib_session = input.session("0820-0920", title="Calculation period for the initial balance", group="Calculation period")

ib_show_extra_levels = input.bool(true, "Show extra levels (IBH x2 & IBL x2)", group="Information")
ib_show_intermediate_levels = input.bool(true, "Show intermediate levels (50%)", group="Information")
ib_show_ib_calculation_area = input.bool(true, "Initial balance calculation period coloration", group="Information")
ib_show_labels = input.bool(true, "Show labels", group="Information")
ib_fill_ib_areas = input.bool(true, "Colour IB areas", group="Information")
ib_only_current_levels = input.bool(false, "Only display the current IB Levels", group="Information")
ib_only_current_zone = input.bool(false, "Only display the current IB calculation area", group="Information")



ib_label_size = input.string("Small", title="Label Size", options=["Auto", "Huge", "Large", "Normal", "Small", "Tiny"], group="Drawings")
ib_lvl_width = input.int(1, "Daily price level width", group="Drawings")
ib_high_col = input.color(color.green, "Initial balance high levels color", group="Drawings")
ib_low_col = input.color(color.red, "Initial balance low levels color", group="Drawings")
ib_middle_col = input.color(#ffa726, "50% initial balance color", group="Drawings")
ib_extend_level = input.string("Right", title="Extend current levels", options=["Right", "Left", "Both", "None"], group="Drawings")

ib_main_levels_style = input.string("Solid" , "Main levels line style", options=["Solid", "Dashed", "Dotted"], group="Drawings")
ib_ext_levels_style = input.string("Dashed" , "Extended levels line style", options=["Solid", "Dashed", "Dotted"], group="Drawings")
ib_int_levels_style = input.string("Dotted" , "Intermediate levels line style", options=["Solid", "Dashed", "Dotted"], group="Drawings")

ib_fill_ib_color= input.color(#b8851faa, "IB area background color", group="Drawings")
ib_ext = ib_extend_level == "Right" ? extend.right : ib_extend_level == "Left" ? extend.left : ib_extend_level == "Both" ? extend.both : extend.none

var ib_delta_history = array.new_float(20)

ib_inSession(sess) => na(time(timeframe.period, sess)) == false

ib_get_line_style(s) =>
    s == "Solid" ? line.style_solid : s == "Dotted" ? line.style_dotted : line.style_dashed

ib_get_levels(n) =>
    h = high[1]
    l = low[1]
    for i=1 to n
        if low[i] < l
            l := low[i]
        if high[i] > h
            h := high[i]
    [h, l, (h+l)/2]

var line ib_ibh = na
var line ib_ibl = na
var line ib_ibm = na
var line ib_ib_plus = na
var line ib_ib_minus = na
var line ib_ib_plus2 = na
var line ib_ib_minus2 = na
var line ib_ibm_plus = na
var line ib_ibm_minus = na

var label ib_labelh = na
var label ib_labell = na
var label ib_labelm = na
var label ib_label_plus = na
var label ib_label_minus = na
var label ib_label_plus2 = na
var label ib_label_minus2 = na
var label ib_labelm_plus = na
var label ib_labelm_minus = na
var box ib_ib_area = na

ib_labelSize = (ib_label_size == "Huge") ? size.huge :
     (ib_label_size == "Large") ? size.large :
     (ib_label_size == "Small") ? size.small :
     (ib_label_size == "Tiny") ? size.tiny :
     (ib_label_size == "Auto") ? size.auto : size.normal

var ib_offset = 0

ib_ins = ib_inSession(ib_session)

bgcolor(ib_show_ib_calculation_area and ib_ins ? #673ab730 : na, title="IB calculation zone")
var float ib_ib_delta = na

if ib_ins
    ib_offset += 1
if ib_ins[1] and not ib_ins
    [h, l, m] = ib_get_levels(ib_offset)
    ib_ib_delta := h - l
    if array.size(ib_delta_history) >= 20
        array.shift(ib_delta_history)
    array.push(ib_delta_history, ib_ib_delta)

    line.set_extend(ib_ibh, extend.none)
    line.set_extend(ib_ibl, extend.none)
    if ib_show_intermediate_levels
        line.set_extend(ib_ibm, extend.none)
    if ib_show_extra_levels
        line.set_extend(ib_ib_plus, extend.none)
        line.set_extend(ib_ib_minus, extend.none)
        line.set_extend(ib_ib_plus2, extend.none)
        line.set_extend(ib_ib_minus2, extend.none)
        if ib_show_intermediate_levels
            line.set_extend(ib_ibm_plus, extend.none)
            line.set_extend(ib_ibm_minus, extend.none)
    if ib_show_labels
        if ib_only_current_levels
            label.delete(ib_labelh)
            label.delete(ib_labell)
            label.delete(ib_labelm)
            label.delete(ib_label_plus)
            label.delete(ib_label_minus)
            label.delete(ib_label_plus2)
            label.delete(ib_label_minus2)
            label.delete(ib_labelm_plus)
            label.delete(ib_labelm_minus)
        ib_labelh := label.new(bar_index[ib_offset], h, text="IBH 100%: "+str.tostring(h), style=label.style_none, textcolor=ib_high_col, size=ib_labelSize)
        ib_labell := label.new(bar_index[ib_offset], l, text="IBL 0%: "+str.tostring(l), style=label.style_none, textcolor=ib_low_col, size=ib_labelSize)
        if ib_show_intermediate_levels
            ib_labelm := label.new(bar_index[ib_offset], m, text="IBM 50%: "+str.tostring(m)+"\nIBΔ: "+str.tostring(h - l), style=label.style_none, textcolor=ib_middle_col, size=ib_labelSize)
        if ib_show_extra_levels
            ib_label_plus := label.new(bar_index[ib_offset], h + ib_ib_delta, text="IBH x2 - "+str.tostring(h + ib_ib_delta), style=label.style_none, textcolor=ib_high_col, size=ib_labelSize)
            ib_label_minus := label.new(bar_index[ib_offset], l - ib_ib_delta, text="IBL x2: "+str.tostring(l - ib_ib_delta), style=label.style_none, textcolor=ib_low_col, size=ib_labelSize)
            ib_label_plus2 := label.new(bar_index[ib_offset], h + (ib_ib_delta*2), text="IBH x3 - "+str.tostring(h + (ib_ib_delta*2)), style=label.style_none, textcolor=ib_high_col, size=ib_labelSize)
            ib_label_minus2 := label.new(bar_index[ib_offset], l - (ib_ib_delta*2), text="IBL x3: "+str.tostring(l - (ib_ib_delta*2)), style=label.style_none, textcolor=ib_low_col, size=ib_labelSize)
    if ib_fill_ib_areas
        if ib_only_current_zone
            box.delete(ib_ib_area)
        ib_ib_area := box.new(bar_index[ib_offset], h, bar_index, l, bgcolor=ib_fill_ib_color, border_color=#00000000)//, extend=ext)
    if ib_only_current_levels
        line.delete(ib_ibh)
        line.delete(ib_ibl)
        line.delete(ib_ibm)
        line.delete(ib_ib_plus)
        line.delete(ib_ib_minus)
        line.delete(ib_ib_plus2)
        line.delete(ib_ib_minus2)
        line.delete(ib_ibm_plus)
        line.delete(ib_ibm_minus)

    ib_ibh := line.new(bar_index[ib_offset], h, bar_index, h, color=ib_high_col, extend=ib_ext, width=ib_lvl_width, style=ib_get_line_style(ib_main_levels_style))
    ib_ibl := line.new(bar_index[ib_offset], l, bar_index, l, color=ib_low_col, extend=ib_ext, width=ib_lvl_width, style=ib_get_line_style(ib_main_levels_style))
    if ib_show_intermediate_levels
        ib_ibm := line.new(bar_index[ib_offset], m, bar_index, m, color=ib_middle_col, style=ib_get_line_style(ib_int_levels_style), extend=ib_ext, width=ib_lvl_width)
    if ib_show_extra_levels
        ib_ib_plus := line.new(bar_index[ib_offset], h + ib_ib_delta, bar_index, h + ib_ib_delta, color=ib_high_col, style=ib_get_line_style(ib_ext_levels_style), extend=ib_ext, width=ib_lvl_width)
        ib_ib_minus := line.new(bar_index[ib_offset], l - ib_ib_delta, bar_index, l - ib_ib_delta, color=ib_low_col, style=ib_get_line_style(ib_ext_levels_style), extend=ib_ext, width=ib_lvl_width)
        ib_ib_plus2 := line.new(bar_index[ib_offset], h + (ib_ib_delta*2), bar_index, h + (ib_ib_delta *2), color=ib_high_col, style=ib_get_line_style(ib_ext_levels_style), extend=ib_ext, width=ib_lvl_width)
        ib_ib_minus2 := line.new(bar_index[ib_offset], l - (ib_ib_delta*2), bar_index, l - (ib_ib_delta*2), color=ib_low_col, style=ib_get_line_style(ib_ext_levels_style), extend=ib_ext, width=ib_lvl_width)
        if ib_show_intermediate_levels
            ib_ibm_plus := line.new(bar_index[ib_offset], h + (ib_ib_delta/2), bar_index, h + (ib_ib_delta/2), color=ib_middle_col, style=ib_get_line_style(ib_int_levels_style), extend=ib_ext, width=ib_lvl_width)
            ib_ibm_minus := line.new(bar_index[ib_offset], l - (ib_ib_delta/2), bar_index, l - (ib_ib_delta/2), color=ib_middle_col, style=ib_get_line_style(ib_int_levels_style), extend=ib_ext, width=ib_lvl_width)
    ib_offset := 0

if (not ib_ins) and (not ib_ins[1])
    line.set_x2(ib_ibh, bar_index)
    line.set_x2(ib_ibl, bar_index)
    if ib_show_intermediate_levels
        line.set_x2(ib_ibm, bar_index)
    if ib_show_extra_levels
        line.set_x2(ib_ib_plus, bar_index)
        line.set_x2(ib_ib_minus, bar_index)
        line.set_x2(ib_ib_plus2, bar_index)
        line.set_x2(ib_ib_minus2, bar_index)
        if ib_show_intermediate_levels
            line.set_x2(ib_ibm_plus, bar_index)
            line.set_x2(ib_ibm_minus, bar_index)

var table ib_ib_analytics = table.new(position.bottom_left, 2, 6)

ib_ib_sentiment() =>
    h = array.max(ib_delta_history)
    l = array.min(ib_delta_history)
    a = array.avg(ib_delta_history)

    h_comp = ib_ib_delta > h ? ib_ib_delta - h : (ib_ib_delta - h) * -1
    l_comp = ib_ib_delta > l ? ib_ib_delta - l : (ib_ib_delta - l) * -1
    a_comp = ib_ib_delta > a ? ib_ib_delta - a : (ib_ib_delta - a) * -1
    (h_comp < l_comp and h_comp < a_comp) ? "Huge" : (l_comp < h_comp and l_comp < a_comp) ? "Small" : "Medium"

//----IB indicator -----
//-----att indicator-----
// HTF Box Settings
att_group_candle = 'HTF Box Settings'

// HTF Box 1
att_htfCndl1  = input.bool(true, '1st HTF Box', inline='TYP1', group=att_group_candle)
att_htfUser1  = input.string('1 Hour', 'TF1', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP1', group=att_group_candle)
att_bullC1    = input.color(#26a69a, 'Bull1', inline='COL1', group=att_group_candle)
att_bearC1    = input.color(#ef5350, 'Bear1', inline='COL1', group=att_group_candle)



// Common Settings
att_group_common = 'Common Settings'
att_trans        = input.int(85, 'Transparency', inline='STYLE', minval=65, maxval=95, group=att_group_common)
att_lw           = input.int(1, 'Line Width', inline='STYLE', minval=1, maxval=4, group=att_group_common)
att_showNumbers  = input.bool(true, 'Show Candle Numbers', group=att_group_common)
att_numbersColor = input.color(color.white, 'Numbers Color', inline='NUM', group=att_group_common)
att_numbersSize  = input.string(size.small, 'Numbers Size', options=[size.tiny, size.small, size.normal], inline='NUM', group=att_group_common)

// ATT Circle Settings
att_group_att = 'ATT Circle Settings'
att_showATT      = input.bool(true, 'Show ATT Circles', group=att_group_att)
att_showATTNumbers = input.bool(true, 'Show ATT Numbers on Circles', group=att_group_att)
att_attColor1    = input.color(#8ee60ac4, 'HTF1 Color', inline='ATT_COL1', group=att_group_att)

// ATT Prediction Settings
att_group_prediction = 'ATT Prediction Settings'
att_showPrediction = input.bool(true, 'Show ATT Predictions', group=att_group_prediction)
att_predictionDistance = input.int(5, 'Show predictions X candles ahead', minval=1, maxval=20, group=att_group_prediction)
att_predictionColor = input.color(color.yellow, 'Prediction Label Color', group=att_group_prediction)
att_predictionSize = input.string(size.tiny, 'Prediction Label Size', options=[size.tiny, size.small, size.normal], group=att_group_prediction)

// The ATT candle numbers where arrow marks will be drawn
var att_att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

att_checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 : _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)
    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

att_f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0., htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.
    var int candleCount = 0

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        candleCount := 0
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        candleCount += 1
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c, candleCount]

att_f_getTF(_htf) =>
    _htf == '3 Mins' ? '3' :_htf == '5 Mins' ? '5' :_htf == '10 Mins' ? '10' :_htf == '15 Mins' ? '15' :_htf == '30 Mins' ? '30' :_htf == '45 Mins' ? '45' :_htf == '1 Hour' ? '60' :_htf == '2 Hours' ? '120' :_htf == '3 Hours' ? '180' :_htf == '4 Hours' ? '240' :_htf == '1 Day' ? 'D' :_htf == '1 Week' ? 'W' :_htf == '1 Month' ? 'M' :_htf == '3 Months' ? '3M' :_htf == '6 Months' ? '6M' :_htf == '1 Year' ? '12M' : na

// Global label arrays for each HTF box
var label[] att_candleLabelsHTF1 = array.new_label()
var label att_currentPredictionLabel = na

// Function to check if current bar should show ATT circle
att_f_checkATTCondition(_show, _htf) =>
    result = false
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        result := array.includes(att_att_numbers, currentCandle)
    result

// Function to get ATT number for current bar
att_f_getATTNumber(_show, _htf) =>
    attNumber = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        if array.includes(att_att_numbers, currentCandle)
            attNumber := currentCandle
    attNumber

// Function to get next ATT number and candles remaining
att_f_getNextATT(_show, _htf) =>
    nextATT = 0
    candlesRemaining = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1

        // Find the next ATT number
        for i = 0 to array.size(att_att_numbers) - 1
            attNum = array.get(att_att_numbers, i)
            if attNum > currentCandle
                nextATT := attNum
                candlesRemaining := attNum - currentCandle
                break

        // If no ATT number found in current cycle, get the first one from next cycle
        if nextATT == 0
            nextATT := array.get(att_att_numbers, 0)
            candlesRemaining := (60 - currentCandle) + nextATT

    [nextATT, candlesRemaining]

// Function to check if we should show prediction
att_f_shouldShowPrediction(_show, _htf) =>
    shouldShow = false
    if _show and att_showPrediction
        [nextATT, candlesRemaining] = att_f_getNextATT(_show, _htf)
        shouldShow := candlesRemaining > 0 and candlesRemaining <= att_predictionDistance
    shouldShow



// Function to draw candle numbers independently
att_f_drawCandleNumbers(_show, _htf, _labelArr) =>
    if _show and att_showNumbers
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        labelPos = low - (high - low) * 0.5
        candleLabel = label.new(bar_index, labelPos, str.tostring(candleCount + 1), style=label.style_label_center, textcolor=att_numbersColor, size=att_numbersSize, yloc=yloc.price, color=na, textalign=text.align_center)
        array.push(_labelArr, candleLabel)

        // Cleanup labels to prevent overload
        if array.size(_labelArr) > 480
            label.delete(array.shift(_labelArr))

// Function to get prediction data (returns values instead of modifying globals)
att_f_getPredictionData(_show, _htf) =>
    showPrediction = false
    predictionText = ""
    labelColor = att_predictionColor

    if _show and att_showPrediction
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        [nextATT, candlesRemaining] = att_f_getNextATT(_show, _htf)

        if candlesRemaining > 0 and candlesRemaining <= att_predictionDistance
            showPrediction := true
            predictionText := candlesRemaining == 1 ? str.tostring(nextATT) + " NEXT!" : str.tostring(nextATT) + " in " + str.tostring(candlesRemaining)
            labelColor := candlesRemaining == 1 ? color.orange : att_predictionColor

    [showPrediction, predictionText, labelColor]

att_f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width, _labelArr, _htfNumber) =>
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)

            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)

            // Cleanup labels to prevent overload
            if array.size(_labelArr) > 480
                label.delete(array.shift(_labelArr))

// ------------------------- Main Logic ------------------------- //

// Process HTF Box 1
att_htf1 = att_f_getTF(att_htfUser1)
att_supported1 = att_checkIf(timeframe.period, att_htf1)



if chart.is_standard
    // Draw HTF Box 1
    if att_supported1
        att_f_processCandles(att_htfCndl1, att_htf1, att_bullC1, att_bearC1, att_trans, att_lw, att_candleLabelsHTF1, 1)

// Calculate ATT conditions for HTF timeframe
att_attCondition1 = att_supported1 ? att_f_checkATTCondition(att_htfCndl1, att_htf1) : false
att_attNumber1 = att_supported1 ? att_f_getATTNumber(att_htfCndl1, att_htf1) : 0

// Determine candle color for positioning
att_isRedCandle = close < open
att_isGreenCandle = close >= open

// Plot ATT circles using plotshape (must be in global scope for historical display)
// HTF1 - With numbers - Red candles (above bar)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 3 and att_isRedCandle, title="ATT HTF1-3 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 11 and att_isRedCandle, title="ATT HTF1-11 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 17 and att_isRedCandle, title="ATT HTF1-17 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 29 and att_isRedCandle, title="ATT HTF1-29 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 41 and att_isRedCandle, title="ATT HTF1-41 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 47 and att_isRedCandle, title="ATT HTF1-47 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 53 and att_isRedCandle, title="ATT HTF1-53 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 59 and att_isRedCandle, title="ATT HTF1-59 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="59", textcolor=color.white)

// HTF1 - With numbers - Green candles (below bar)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 3 and att_isGreenCandle, title="ATT HTF1-3 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 11 and att_isGreenCandle, title="ATT HTF1-11 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 17 and att_isGreenCandle, title="ATT HTF1-17 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 29 and att_isGreenCandle, title="ATT HTF1-29 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 41 and att_isGreenCandle, title="ATT HTF1-41 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 47 and att_isGreenCandle, title="ATT HTF1-47 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 53 and att_isGreenCandle, title="ATT HTF1-53 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 59 and att_isGreenCandle, title="ATT HTF1-59 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="59", textcolor=color.white)

// HTF1 - Without numbers
plotshape(att_showATT and not att_showATTNumbers and att_attCondition1 and att_isRedCandle, title="ATT HTF1 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small)
plotshape(att_showATT and not att_showATTNumbers and att_attCondition1 and att_isGreenCandle, title="ATT HTF1 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small)

// Draw candle numbers for the HTF timeframe
if att_supported1 and att_htfCndl1
    att_f_drawCandleNumbers(true, att_htf1, att_candleLabelsHTF1)

// Handle prediction label in global scope
if not na(att_currentPredictionLabel)
    label.delete(att_currentPredictionLabel)
    att_currentPredictionLabel := na

if att_supported1 and att_htfCndl1
    [showPrediction, predictionText, labelColor] = att_f_getPredictionData(true, att_htf1)
    if showPrediction
        labelPos = high + (high - low) * 0.3
        att_currentPredictionLabel := label.new(bar_index, labelPos, predictionText, style=label.style_label_down, textcolor=color.black, size=att_predictionSize, yloc=yloc.price, color=labelColor, textalign=text.align_center)

//-----att indicator-----